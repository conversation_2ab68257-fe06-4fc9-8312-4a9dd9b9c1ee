using JT.Server.Entities;
using Microsoft.AspNetCore.Mvc;

namespace JT.Server.Apis;

[ApiController]
[Route("api/[controller]")]
public class JobCategoryController : JTControllerBase
{
    /// <summary>
    /// Logger for the JobCategoryController.
    /// </summary>
    private readonly ILogger<JobCategoryController> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="JobCategoryController"/> class.
    /// </summary>
    /// <param name="logger">The logger instance for logging operations.</param>
    public JobCategoryController(ILogger<JobCategoryController> logger) => _logger = logger;

    /// <summary>
    /// Retrieves all recipe categories.
    /// </summary>
    /// <returns>A list of categories.</returns>
    [HttpGet("categories")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(IEnumerable<CategoryData>), 200)]
    [ProducesResponseType(404)]
    public ActionResult<IEnumerable<CategoryData>> GetJobCategories()
    {
        try
        {
            var categories = LoadData<List<CategoryData>>("JobCategories.json");
            return Ok(categories.ToImmutableList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting job categories");
            return StatusCode(500, "Internal server error");
        }
    }


 /// <summary>
    /// Retrieves a specific job category by its ID.
    /// </summary>
    /// <param name="id">The ID of the job category to retrieve.</param>
    /// <returns>The job category if found; otherwise, a 404 Not Found response.</returns>
    [HttpGet("{id}")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(CategoryData), 200)]
    [ProducesResponseType(404)]
    public ActionResult<CategoryData> GetJobCategory(int id)
    {
        try
        {
            var categories = LoadData<List<CategoryData>>("JobCategories.json");
            var category = categories.FirstOrDefault(c => c.Id == id);

            if (category == null)
            {
                return NotFound();
            }

            return Ok(category);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting job category {Id}", id);
            return StatusCode(500, "Internal server error");
        }
    }
}
