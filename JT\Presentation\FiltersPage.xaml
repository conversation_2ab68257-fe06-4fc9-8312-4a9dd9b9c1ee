﻿<Page x:Class="JT.Presentation.FiltersPage"
	  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
	  xmlns:local="using:JT.Presentation"
	  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
	  xmlns:muxc="using:Microsoft.UI.Xaml.Controls"
	  xmlns:uen="using:Uno.Extensions.Navigation.UI"
	  xmlns:uer="using:Uno.Extensions.Reactive.UI"
	  xmlns:utu="using:Uno.Toolkit.UI"
	  xmlns:win="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:not_mobile="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:mobile="http://platform.uno/mobile"
	  Background="{ThemeResource SurfaceBrush}"
	  mc:Ignorable="d mobile">

	<Page.Resources>
		<DataTemplate x:Key="FilterChipTemplate">
			<utu:Chip Background="{ThemeResource SurfaceBrush}"
					  Content="{Binding}"
					  HorizontalAlignment="Stretch"
					  Foreground="{ThemeResource OnSurfaceVariantBrush}"
					  BorderThickness="1"
					  Style="{StaticResource MaterialChipStyle}" />
		</DataTemplate>
	</Page.Resources>
	<utu:AutoLayout>
		<utu:AutoLayout uen:Region.Attached="True"
						utu:AutoLayout.PrimaryAlignment="Stretch"
						PrimaryAxisAlignment="Stretch">
			<utu:NavigationBar Content="Filters"
							   Style="{StaticResource ChefsModalNavigationBarStyle}" />
			<ScrollViewer HorizontalScrollMode="Disabled"
						  utu:AutoLayout.PrimaryAlignment="Stretch">
				<utu:AutoLayout Padding="16"
								Background="{ThemeResource SurfaceBrush}"
								Spacing="32">
					<utu:AutoLayout Spacing="16">
						<TextBlock Style="{StaticResource TitleSmall}"
								   Text="Recipe Categories"
								   TextWrapping="Wrap" />
						<!-- It's important to put the ItemsSource above the SelectedItems, because, it will otherwise fail on WASM, see this issue: https://github.com/unoplatform/uno.toolkit.ui/issues/918 -->
						<muxc:ItemsRepeater ItemsSource="{Binding FilterGroups}"
											utu:ItemsRepeaterExtensions.SelectedItem="{Binding Filter.FilterGroup, Mode=TwoWay}"
											utu:ItemsRepeaterExtensions.SelectionMode="SingleOrNone"
											ItemTemplate="{StaticResource FilterChipTemplate}">
							<muxc:ItemsRepeater.Layout>
								<muxc:UniformGridLayout ItemsJustification="Start"
														MinColumnSpacing="8"
														MinRowSpacing="8"
														MinItemWidth="120"
														ItemsStretch="Fill"
														MaximumRowsOrColumns="4"
														Orientation="Horizontal" />
							</muxc:ItemsRepeater.Layout>
						</muxc:ItemsRepeater>
					</utu:AutoLayout>
					<utu:AutoLayout Spacing="16">
						<TextBlock Style="{StaticResource TitleSmall}"
								   Text="Cooking Time"
								   TextWrapping="Wrap" />
						<muxc:ItemsRepeater ItemsSource="{Binding Times}"
											utu:ItemsRepeaterExtensions.SelectedItem="{Binding Filter.Time, Mode=TwoWay}"
											utu:ItemsRepeaterExtensions.SelectionMode="SingleOrNone">
							<muxc:ItemsRepeater.Layout>
								<muxc:UniformGridLayout ItemsJustification="Start"
														MinColumnSpacing="8"
														MinRowSpacing="8"
														MinItemWidth="90"
														ItemsStretch="Fill"
														MaximumRowsOrColumns="4"
														Orientation="Horizontal" />
							</muxc:ItemsRepeater.Layout>
							<muxc:ItemsRepeater.ItemTemplate>
								<DataTemplate>
									<utu:Chip Background="{ThemeResource SurfaceBrush}"
											  Content="{Binding Converter={StaticResource CookingTimeFormatter}}"
											  Foreground="{ThemeResource OnSurfaceVariantBrush}"
											  HorizontalAlignment="Stretch"
											  BorderThickness="1"
											  Style="{StaticResource MaterialChipStyle}" />
								</DataTemplate>
							</muxc:ItemsRepeater.ItemTemplate>
						</muxc:ItemsRepeater>
					</utu:AutoLayout>
					<utu:AutoLayout Spacing="16">
						<TextBlock Style="{StaticResource TitleSmall}"
								   Text="Skill Level"
								   TextWrapping="Wrap" />
						<muxc:ItemsRepeater ItemsSource="{Binding Difficulties}"
											utu:ItemsRepeaterExtensions.SelectedItem="{Binding Filter.Difficulty, Mode=TwoWay}"
											utu:ItemsRepeaterExtensions.SelectionMode="SingleOrNone"
											ItemTemplate="{StaticResource FilterChipTemplate}">
							<muxc:ItemsRepeater.Layout>
								<muxc:UniformGridLayout ItemsJustification="Start"
														MinColumnSpacing="8"
														MinRowSpacing="8"
														MinItemWidth="140"
														ItemsStretch="Fill"
														MaximumRowsOrColumns="4"
														Orientation="Horizontal" />
							</muxc:ItemsRepeater.Layout>
						</muxc:ItemsRepeater>
					</utu:AutoLayout>
					<utu:AutoLayout Spacing="16">
						<TextBlock Style="{StaticResource TitleSmall}"
								   Text="Serves"
								   TextWrapping="Wrap" />
						<muxc:ItemsRepeater ItemsSource="{Binding Serves}"
											utu:ItemsRepeaterExtensions.SelectedItem="{Binding Filter.Serves, Mode=TwoWay}"
											utu:ItemsRepeaterExtensions.SelectionMode="SingleOrNone"
											ItemTemplate="{StaticResource FilterChipTemplate}">
							<muxc:ItemsRepeater.Layout>
								<muxc:UniformGridLayout ItemsJustification="Start"
														MinColumnSpacing="8"
														MinRowSpacing="8"
														MinItemWidth="50"
														ItemsStretch="Fill"
														MaximumRowsOrColumns="4"
														Orientation="Horizontal" />
							</muxc:ItemsRepeater.Layout>
						</muxc:ItemsRepeater>
					</utu:AutoLayout>
				</utu:AutoLayout>
			</ScrollViewer>
		</utu:AutoLayout>
		<utu:AutoLayout Height="100"
						Padding="16,24"
						Background="{ThemeResource SurfaceBrush}"
						Orientation="Horizontal"
						PrimaryAxisAlignment="Center"
						CounterAxisAlignment="Start"
						Spacing="16">
			<Button Content="Reset"
					Height="60"
					Command="{Binding Reset}"
					CornerRadius="4"
					Style="{StaticResource TextButtonStyle}"
					utu:AutoLayout.PrimaryAlignment="Stretch" />
			<Button Content="Apply filter"
					Height="60"
					Command="{Binding ApplySearchFilter}"
					CornerRadius="4"
					utu:AutoLayout.PrimaryAlignment="Stretch" />
		</utu:AutoLayout>
	</utu:AutoLayout>
</Page>
