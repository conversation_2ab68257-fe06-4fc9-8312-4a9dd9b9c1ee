using TokenTransactionData = JT.Content.Client.Models.TokenTransactionData;

namespace JT.Business.Models;

public partial record TokenTransaction
{
	internal TokenTransaction(TokenTransactionData tokenTransactionData)
	{
		Id = tokenTransactionData.Id;
		UserId = tokenTransactionData.UserId ?? Guid.Empty;
		Amount = tokenTransactionData.Amount ?? 0;
		Type = tokenTransactionData.Type;
		Description = tokenTransactionData.Description;
		ReferenceId = tokenTransactionData.ReferenceId;
		CreatedAt = tokenTransactionData.CreatedAt ?? DateTime.MinValue;
		BalanceAfter = tokenTransactionData.BalanceAfter ?? 0;
	}

	public string? Id { get; init; }
	public Guid UserId { get; init; }
	public int Amount { get; init; }
	public string? Type { get; init; }
	public string? Description { get; init; }
	public string? ReferenceId { get; init; }
	public DateTime CreatedAt { get; init; }
	public int BalanceAfter { get; init; }

	// Computed properties
	public string TypeDisplayName => Type switch
	{
		"referral" => "Referral Bonus",
		"redemption" => "Token Redemption",
		"purchase" => "Purchase Bonus",
		"bonus" => "Welcome Bonus",
		_ => Type ?? "Unknown"
	};

	public string TypeColor => Type switch
	{
		"referral" => "#28A745",
		"purchase" => "#007BFF",
		"bonus" => "#FFC107",
		"redemption" => "#DC3545",
		_ => "#6C757D"
	};

	public bool IsCredit => Amount > 0;
	public bool IsDebit => Amount < 0;
	public string AmountDisplay => Amount > 0 ? $"+{Amount}" : Amount.ToString();

	internal TokenTransactionData ToData() => new()
	{
		Id = Id,
		UserId = UserId,
		Amount = Amount,
		Type = Type,
		Description = Description,
		ReferenceId = ReferenceId,
		CreatedAt = CreatedAt,
		BalanceAfter = BalanceAfter
	};
}
