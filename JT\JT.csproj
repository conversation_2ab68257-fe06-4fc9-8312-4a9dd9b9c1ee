<Project Sdk="Uno.Sdk">
  <PropertyGroup>
    <!-- Building with dotnet build -f net9.0-platform will still cause restore to happen for all TargetFrameworks -->
    <!-- which will force us to install all workloads even if not needed -->
    <!-- To prevent that, we will pass TargetFrameworkOverride as a global property (i.e, dotnet build -p:TargetFrameworkOverride=net9.0-platform) -->
    <!-- That way, we set TargetFrameworks property to only the needed TargetFramework -->
    <TargetFrameworks Condition="'$(TargetFrameworkOverride)'!=''">$(TargetFrameworkOverride)</TargetFrameworks>
    <TargetFrameworks Condition="'$(TargetFrameworkOverride)'==''">
    net9.0-android;net9.0-ios;net9.0-windows10.0.26100;net9.0-browserwasm;net9.0-desktop;net9.0</TargetFrameworks>

    <OutputType>Exe</OutputType>
    <UnoSingleProject>true</UnoSingleProject>

    <!-- Display name -->
    <ApplicationTitle>JT</ApplicationTitle>
    <!-- App Identifier -->
    <ApplicationId>com.companyname.JT</ApplicationId>
    <ApplicationId Condition="'$(IsCanaryBranch)'=='true'">$(ApplicationId)-canary</ApplicationId>
      <!-- Versions -->
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <ApplicationVersion>1</ApplicationVersion>
    <UseMocks Condition="'$(UseMocks)'==''">true</UseMocks>
    <!-- Package Publisher -->
    <ApplicationPublisher>MOE</ApplicationPublisher>
    <!-- Package Description -->
    <Description>JT powered by Uno Platform.</Description>
    <!--
      If you encounter this error message:

        error NETSDK1148: A referenced assembly was compiled using a newer version of Microsoft.Windows.SDK.NET.dll.
        Please update to a newer .NET SDK in order to reference this assembly.

      This means that the two packages below must be aligned with the "build" version number of
      the "Microsoft.Windows.SDK.BuildTools" package above, and the "revision" version number
      must be the highest found in https://www.nuget.org/packages/Microsoft.Windows.SDK.NET.Ref.
    -->
    <!-- <WindowsSdkPackageVersion>10.0.22621.28</WindowsSdkPackageVersion> -->

    <!--
      UnoFeatures let's you quickly add and manage implicit package references based on the features you want to use.
      https://aka.platform.uno/singleproject-features
    -->
    <UnoFeatures>
      Material;
      Dsp;
      Hosting;
      Toolkit;
      Logging;
      MVUX;
      Configuration;
      HttpKiota;
      Serialization;
      Localization;
      AuthenticationOidc;
      Navigation;
      Skia;
      ThemeService;
      SkiaRenderer;
    </UnoFeatures>
      <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)'=='Debug' or '$(IsUiAutomationMappingEnabled)'=='True'">
    <IsUiAutomationMappingEnabled>True</IsUiAutomationMappingEnabled>
    <DefineConstants>$(DefineConstants);USE_UITESTS</DefineConstants>
  </PropertyGroup>

    <PropertyGroup Condition="'$(UseMocks)'=='true'">
        <DefineConstants>$(DefineConstants);USE_MOCKS</DefineConstants>
    </PropertyGroup>
    
  <ItemGroup>
    <ProjectReference Include="..\JT.DataContracts\JT.DataContracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Presentation\ShellControl.xaml.cs">
      <DependentUpon>ShellControl.xaml</DependentUpon>
    </Compile>
  </ItemGroup>

    <ItemGroup>
        <Content Include="..\AppData\*.json" LinkBase="AppData">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="Business\Services\Analytics\**" />
      <Compile Remove="Business\Services\Cookbooks\**" />
      <Compile Remove="Business\Services\Notifications\**" />
      <Compile Remove="Business\Services\Payments\**" />
      <Compile Remove="Business\Services\Promotions\**" />
      <Compile Remove="Business\Services\Recipes\**" />
      <Compile Remove="Business\Services\Referrals\**" />
      <Compile Remove="Business\Services\Sharing\**" />
      <Compile Remove="Business\Services\Skills\**" />
      <Compile Remove="Content\Client\JTServiceClient\**" />
      <EmbeddedResource Remove="Business\Services\Analytics\**" />
      <EmbeddedResource Remove="Business\Services\Cookbooks\**" />
      <EmbeddedResource Remove="Business\Services\Notifications\**" />
      <EmbeddedResource Remove="Business\Services\Payments\**" />
      <EmbeddedResource Remove="Business\Services\Promotions\**" />
      <EmbeddedResource Remove="Business\Services\Recipes\**" />
      <EmbeddedResource Remove="Business\Services\Referrals\**" />
      <EmbeddedResource Remove="Business\Services\Sharing\**" />
      <EmbeddedResource Remove="Business\Services\Skills\**" />
      <EmbeddedResource Remove="Content\Client\JTServiceClient\**" />
      <None Remove="Business\Services\Analytics\**" />
      <None Remove="Business\Services\Cookbooks\**" />
      <None Remove="Business\Services\Notifications\**" />
      <None Remove="Business\Services\Payments\**" />
      <None Remove="Business\Services\Promotions\**" />
      <None Remove="Business\Services\Recipes\**" />
      <None Remove="Business\Services\Referrals\**" />
      <None Remove="Business\Services\Sharing\**" />
      <None Remove="Business\Services\Skills\**" />
      <None Remove="Content\Client\JTServiceClient\**" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="Business\Models\Analytics.cs" />
      <Compile Remove="Business\Models\BusinessAdRequest.cs" />
      <Compile Remove="Business\Models\Category.cs" />
      <Compile Remove="Business\Models\CategoryWithCount.cs" />
      <Compile Remove="Business\Models\Cookbook.cs" />
      <Compile Remove="Business\Models\CookbookImages.cs" />
      <Compile Remove="Business\Models\EmployeeProfile.cs" />
      <Compile Remove="Business\Models\Ingredient.cs" />
      <Compile Remove="Business\Models\Notification.cs" />
      <Compile Remove="Business\Models\Nutrition.cs" />
      <Compile Remove="Business\Models\Organization.cs" />
      <Compile Remove="Business\Models\PaymentTransaction.cs" />
      <Compile Remove="Business\Models\Promotion.cs" />
      <Compile Remove="Business\Models\PromotionAnalytics.cs" />
      <Compile Remove="Business\Models\PromotionPackage.cs" />
      <Compile Remove="Business\Models\Recipe.cs" />
      <Compile Remove="Business\Models\Referral.cs" />
      <Compile Remove="Business\Models\Review.cs" />
      <Compile Remove="Business\Models\SavedSearch.cs" />
      <Compile Remove="Business\Models\SearchFilter.cs" />
      <Compile Remove="Business\Models\Skill.cs" />
      <Compile Remove="Business\Models\Step.cs" />
      <Compile Remove="Business\Models\UpdateCookBook.cs" />
      <Compile Remove="Content\Client\Mock\MockCookbookEndpoints.cs" />
      <Compile Remove="Content\Client\Mock\MockNotificationEndpoints.cs" />
      <Compile Remove="Content\Client\Mock\MockTransferEndpoints.cs" />
      <Compile Remove="Converters\TimeSpanConverter.cs" />
      <Compile Remove="Converters\TimeSpanToStringConverter.cs" />
      <Compile Remove="Presentation\Controls\ChartControl.xaml.cs" />
      <Compile Remove="Presentation\FilterModel.cs" />
      <Compile Remove="Presentation\FiltersPage.xaml.cs" />
      <Compile Remove="Presentation\PromotionModel.cs" />
      <Compile Remove="Presentation\SearchModel.cs" />
      <Compile Remove="Presentation\SearchPage.xaml.cs" />
      <Compile Remove="Presentation\SubscriptionModel.cs" />
      <Compile Remove="Presentation\TokenModel.cs" />
      <Compile Remove="Presentation\TransferRequestModel.cs" />
    </ItemGroup>

    <ItemGroup>
      <None Remove="Presentation\Controls\ChartControl.xaml" />
      <None Remove="Presentation\FiltersPage.xaml" />
      <None Remove="Presentation\SearchPage.xaml" />
    </ItemGroup>

    <ItemGroup>
      <Page Update="Presentation\HomePage.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Presentation\Flyouts\ResponsiveDrawerFlyout.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\Button.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\ChartBrushes.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\CustomFonts.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\FeedView.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\Images.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\MaterialFontsOverride.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\NavigationBar.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\Page.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\Strings.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Styles\TextBox.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Presentation\WelcomePage.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Presentation\Controls\ChartControl.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Presentation\Controls\WelcomeView.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="LiveChartsCore.SkiaSharpView.Uno.WinUI" />
    </ItemGroup>

    <ItemGroup>
      <Page Update="Converters\Converters.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Presentation\Templates\ItemTemplates.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <AndroidResource Remove="Business\Services\Analytics\**" />
    </ItemGroup>

    <ItemGroup>
      <UpToDateCheckInput Remove="Business\Services\Analytics\**" />
    </ItemGroup>

    <ItemGroup>
      <Content Remove="Business\Services\Analytics\**" />
    </ItemGroup>

    <ItemGroup>
      <Page Remove="Business\Services\Analytics\**" />
    </ItemGroup>

    <ItemGroup>
      <PRIResource Remove="Business\Services\Analytics\**" />
    </ItemGroup>

    <ItemGroup>
      <AndroidResource Remove="Business\Services\Payments\**" />
    </ItemGroup>

    <ItemGroup>
      <UpToDateCheckInput Remove="Business\Services\Payments\**" />
    </ItemGroup>

    <ItemGroup>
      <Content Remove="Business\Services\Payments\**" />
    </ItemGroup>

    <ItemGroup>
      <Page Remove="Business\Services\Payments\**" />
    </ItemGroup>

    <ItemGroup>
      <PRIResource Remove="Business\Services\Payments\**" />
    </ItemGroup>

    <ItemGroup>
      <AndroidResource Remove="Business\Services\Promotions\**" />
    </ItemGroup>

    <ItemGroup>
      <UpToDateCheckInput Remove="Business\Services\Promotions\**" />
    </ItemGroup>

    <ItemGroup>
      <Content Remove="Business\Services\Promotions\**" />
    </ItemGroup>

    <ItemGroup>
      <Page Remove="Business\Services\Promotions\**" />
    </ItemGroup>

    <ItemGroup>
      <PRIResource Remove="Business\Services\Promotions\**" />
    </ItemGroup>

    <ItemGroup>
      <AndroidResource Remove="Business\Services\Skills\**" />
    </ItemGroup>

    <ItemGroup>
      <UpToDateCheckInput Remove="Business\Services\Skills\**" />
    </ItemGroup>

    <ItemGroup>
      <Content Remove="Business\Services\Skills\**" />
    </ItemGroup>

    <ItemGroup>
      <Page Remove="Business\Services\Skills\**" />
    </ItemGroup>

    <ItemGroup>
      <PRIResource Remove="Business\Services\Skills\**" />
    </ItemGroup>

    <ItemGroup>
      <AndroidResource Remove="Business\Services\Referrals\**" />
    </ItemGroup>

    <ItemGroup>
      <UpToDateCheckInput Remove="Business\Services\Referrals\**" />
    </ItemGroup>

    <ItemGroup>
      <Content Remove="Business\Services\Referrals\**" />
    </ItemGroup>

    <ItemGroup>
      <Page Remove="Business\Services\Referrals\**" />
    </ItemGroup>

    <ItemGroup>
      <PRIResource Remove="Business\Services\Referrals\**" />
    </ItemGroup>

    <ItemGroup>
      <AndroidResource Remove="Business\Services\Recipes\**" />
    </ItemGroup>

    <ItemGroup>
      <UpToDateCheckInput Remove="Business\Services\Recipes\**" />
    </ItemGroup>

    <ItemGroup>
      <Content Remove="Business\Services\Recipes\**" />
    </ItemGroup>

    <ItemGroup>
      <Page Remove="Business\Services\Recipes\**" />
    </ItemGroup>

    <ItemGroup>
      <PRIResource Remove="Business\Services\Recipes\**" />
    </ItemGroup>

    <ItemGroup>
      <AndroidResource Remove="Business\Services\Cookbooks\**" />
    </ItemGroup>

    <ItemGroup>
      <UpToDateCheckInput Remove="Business\Services\Cookbooks\**" />
    </ItemGroup>

    <ItemGroup>
      <Content Remove="Business\Services\Cookbooks\**" />
    </ItemGroup>

    <ItemGroup>
      <Page Remove="Business\Services\Cookbooks\**" />
    </ItemGroup>

    <ItemGroup>
      <PRIResource Remove="Business\Services\Cookbooks\**" />
    </ItemGroup>

    <ItemGroup>
      <AndroidResource Remove="Business\Services\Notifications\**" />
    </ItemGroup>

    <ItemGroup>
      <UpToDateCheckInput Remove="Business\Services\Notifications\**" />
    </ItemGroup>

    <ItemGroup>
      <Content Remove="Business\Services\Notifications\**" />
    </ItemGroup>

    <ItemGroup>
      <Page Remove="Business\Services\Notifications\**" />
    </ItemGroup>

    <ItemGroup>
      <PRIResource Remove="Business\Services\Notifications\**" />
    </ItemGroup>

    <ItemGroup>
      <AndroidResource Remove="Business\Services\Sharing\**" />
    </ItemGroup>

    <ItemGroup>
      <UpToDateCheckInput Remove="Business\Services\Sharing\**" />
    </ItemGroup>

    <ItemGroup>
      <Content Remove="Business\Services\Sharing\**" />
    </ItemGroup>

    <ItemGroup>
      <Page Remove="Business\Services\Sharing\**" />
    </ItemGroup>

    <ItemGroup>
      <PRIResource Remove="Business\Services\Sharing\**" />
    </ItemGroup>

    <ItemGroup>
      <Page Remove="Presentation\Controls\ChartControl.xaml" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Specs\" />
    </ItemGroup>

    <ItemGroup>
      <AndroidResource Remove="Content\Client\JTServiceClient\**" />
    </ItemGroup>

    <ItemGroup>
      <UpToDateCheckInput Remove="Content\Client\JTServiceClient\**" />
    </ItemGroup>

    <ItemGroup>
      <Content Remove="Content\Client\JTServiceClient\**" />
    </ItemGroup>

    <ItemGroup>
      <Page Remove="Content\Client\JTServiceClient\**" />
    </ItemGroup>

    <ItemGroup>
      <PRIResource Remove="Content\Client\JTServiceClient\**" />
    </ItemGroup>

    <ItemGroup>
      <Page Update="Presentation\RegistrationPage.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Update="Presentation\SearchPage.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Remove="Presentation\SearchPage.xaml" />
    </ItemGroup>

    <ItemGroup>
      <Page Update="Presentation\FiltersPage.xaml">
        <Generator>MSBuild:Compile</Generator>
      </Page>
    </ItemGroup>

    <ItemGroup>
      <Page Remove="Presentation\FiltersPage.xaml" />
    </ItemGroup>
</Project>
