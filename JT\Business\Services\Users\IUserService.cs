namespace JT.Business.Services.Users;

/// <summary>
/// Implements user related methods
/// </summary>
public interface IUserService
{
	/// <summary>
	/// Current user data
	/// </summary>
	/// <param name="ct"></param>
	/// <returns>
	/// User logged in
	/// </returns>
	ValueTask<User> GetCurrent(CancellationToken ct);

	/// <summary>
	/// Feed of the current user.
	/// </summary>
	IFeed<User> User { get; }

	/// <summary>
	/// Update user information
	/// </summary>
	/// <param name="user">user with information to update</param>
	/// <param name="ct"></param>
	/// <returns>
	/// </returns>
	ValueTask Update(User user, CancellationToken ct);

	/// <summary>
	/// Porpular creators related with the recipes
	/// </summary>
	/// <param name="ct"></param>
	/// <returns>
	/// Return users that they are popular by their recipes
	/// </returns>
	ValueTask<IImmutableList<User>> GetPopularCreators(CancellationToken ct);

	/// <summary>
	/// Returns specific user
	/// </summary>
	/// <param name="userId">User GUID</param>
	/// <returns>
	/// User
	/// </returns>
	ValueTask<User> GetById(Guid userId, CancellationToken ct);

    // <summary>
    // Authentication method
    // </summary>
    // <param name="email"> The user email </param>
    // <param name="ct"></param>
    // <returns>
    // User logged in
    // </returns>
    // In case we need auth
    //ValueTask<bool> BasicAuthenticate(string email, string password, CancellationToken ct);

    // Job Transfer specific methods
    /// <summary>
    /// Register a new user
    /// </summary>
    //ValueTask<User> Register(User user, CancellationToken ct = default);

	/// <summary>
	/// Authenticate user with email and password
	/// </summary>
	//ValueTask<bool> Authenticate(string email, string password, CancellationToken ct = default);

	/// <summary>
	/// Update user subscription tier
	/// </summary>
	//ValueTask<bool> UpdateSubscription(Guid userId, string subscriptionTier, CancellationToken ct = default);

	/// <summary>
	/// Add tokens to user balance
	/// </summary>
	//ValueTask<bool> AddTokens(Guid userId, int tokens, CancellationToken ct = default);

	/// <summary>
	/// Deduct tokens from user balance
	/// </summary>
	//ValueTask<bool> DeductTokens(Guid userId, int tokens, CancellationToken ct = default);

	/// <summary>
	/// Generate referral code for user
	/// </summary>
	//ValueTask<string> GenerateReferralCode(Guid userId, CancellationToken ct = default);

	/// <summary>
	/// Process referral when new user joins
	/// </summary>
	//ValueTask<bool> ProcessReferral(string referralCode, Guid newUserId, CancellationToken ct = default);

	/// <summary>
	/// Get users by subscription tier
	/// </summary>
	ValueTask<IImmutableList<User>> GetBySubscriptionTier(string tier, CancellationToken ct = default);

	/// <summary>
	/// Get users by location
	/// </summary>
	ValueTask<IImmutableList<User>> GetByLocation(string location, CancellationToken ct = default);
}
