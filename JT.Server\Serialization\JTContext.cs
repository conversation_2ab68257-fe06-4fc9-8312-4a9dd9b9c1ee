using System.Text.Json.Serialization;

namespace JT.Server.Serialization;
/// <summary>
/// Generated class for System.Text.Json Serialization
/// </summary>
/// <remarks>
/// When using the JsonSerializerContext you must add the JsonSerializableAttribute
/// for each type that you may need to serialize / deserialize including both the
/// concrete type and any interface that the concrete type implements.
/// For more information on the JsonSerializerContext see:
/// https://learn.microsoft.com/en-us/dotnet/standard/serialization/system-text-json/source-generation?WT.mc_id=DT-MVP-5002924
/// </remarks>
[JsonSerializable(typeof(ImmutableList<SubscriptionPlanData>))]
//[JsonSerializable(typeof(CookbookData))]
[JsonSerializable(typeof(TransferRequestData))]
[JsonSerializable(typeof(ImmutableList<NotificationData>))]
[JsonSerializable(typeof(ImmutableList<TransferRequestData>))]
[JsonSerializable(typeof(ImmutableList<CategoryData>))]
[JsonSerializable(typeof(ImmutableList<TokenTransactionData>))]
[JsonSerializable(typeof(ImmutableList<UserData>))]
[JsonSerializable(typeof(ImmutableList<SkillData>))]
//[JsonSerializable(typeof(ImmutableList<ReviewData>))]
[JsonSerializable(typeof(UserData))]
[JsonSerializable(typeof(Guid))]
//[JsonSerializable(typeof(ReviewData))]
[JsonSerializable(typeof(IEnumerable<TransferRequestData>))]
[JsonSerializable(typeof(ProblemDetails))]
[JsonSourceGenerationOptions(PropertyNamingPolicy = JsonKnownNamingPolicy.CamelCase, PropertyNameCaseInsensitive = true)]
public partial class JTContext : JsonSerializerContext
{
}
