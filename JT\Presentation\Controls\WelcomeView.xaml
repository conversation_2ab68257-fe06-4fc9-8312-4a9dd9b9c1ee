﻿<UserControl x:Class="JT.Presentation.Controls.WelcomeView"
			 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
			 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
			 xmlns:local="using:JT.Presentation.Controls"
			 xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
			 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
			 xmlns:utu="using:Uno.Toolkit.UI"
			 mc:Ignorable="d"
			 d:DesignHeight="300"
			 d:DesignWidth="400">

	<utu:AutoLayout CounterAxisAlignment="Center"
					Spacing="24">

		<Image Visibility="{utu:Responsive Normal=Visible,
										   Wide=Collapsed}"
			   Source="{Binding ImageUrl}"
			   Height="270"
			   Stretch="UniformToFill" />
		<utu:AutoLayout PrimaryAxisAlignment="Center"
						CounterAxisAlignment="Center"
						utu:AutoLayout.PrimaryAlignment="Stretch"
						Spacing="24">
			<Image Width="160"
				   Height="90"
				   Source="{ThemeResource ChefsLogoWithIcon}" />
			<TextBlock Foreground="{ThemeResource OnSurfaceBrush}"
					   Style="{StaticResource TitleLarge}"
					   Text="{Binding Title}"
					   Padding="32,0"
					   TextWrapping="Wrap" />
			<TextBlock Foreground="{ThemeResource OnSurfaceBrush}"
					   Style="{StaticResource TitleMedium}"
					   Text="{Binding Description}"
					   Padding="32,0"
					   TextWrapping="Wrap" />
		</utu:AutoLayout>
	</utu:AutoLayout>
</UserControl>
