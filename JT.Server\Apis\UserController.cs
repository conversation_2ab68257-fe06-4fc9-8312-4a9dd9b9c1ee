namespace JT.Server.Apis;

/// <summary>
/// User Endpoints
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class UserController() : JTControllerBase
{
	private readonly string _usersFilePath = "Users.json";
	private Guid? _currentUserId = new Guid("3c896419-e280-40e7-8552-240635566fed");

	/// <summary>
	/// Retrieves all users.
	/// </summary>
	/// <returns>A list of users.</returns>
	[HttpGet]
	[Produces("application/json")]
	[ProducesResponseType(typeof(IEnumerable<UserData>), 200)]
	public ActionResult<IEnumerable<UserData>> GetAll()
	{
		var users = LoadData<List<UserData>>(_usersFilePath);
		return Ok(users.ToImmutableList());
	}

	/// <summary>
	/// Authenticates a user with email and password.
	/// </summary>
	/// <param name="loginRequest">The login request containing email and password.</param>
	/// <returns>The user ID if authentication is successful, otherwise Unauthorized.</returns>
	[HttpPost("authenticate")]
	[ProducesResponseType(typeof(Guid), 200)]
	[ProducesResponseType(401)]
	public ActionResult<Guid> Authenticate([FromBody] LoginRequest loginRequest)
	{
		var users = LoadData<List<UserData>>(_usersFilePath);
		var user = users.FirstOrDefault(u => u.Email == loginRequest.Email && u.Password == loginRequest.Password);

		if (user is null)
		{
			return Unauthorized();
		}

		_currentUserId = user.Id;
		return Ok(user.Id);
	}

	/// <summary>
	/// Retrieves a list of popular creators, excluding the current user.
	/// </summary>
	/// <returns>A list of popular creators.</returns>
	[HttpGet("popular-creators")]
	[Produces("application/json")]
	[ProducesResponseType(typeof(IEnumerable<UserData>), 200)]
	public ActionResult<IEnumerable<UserData>> GetPopularCreators()
	{
		var users = LoadData<List<UserData>>(_usersFilePath);
		var popularCreators = users.Where(x => x.Id != _currentUserId).ToImmutableList();
		return Ok(popularCreators);
	}

	/// <summary>
	/// Retrieves the current user.
	/// </summary>
	/// <returns>The current user.</returns>
	[HttpGet("current")]
	[Produces("application/json")]
	[ProducesResponseType(typeof(UserData), 200)]
	[ProducesResponseType(404)]
	public ActionResult<UserData> GetCurrent()
	{
		var users = LoadData<List<UserData>>(_usersFilePath);
		var currentUser = users.FirstOrDefault(u => u.Id == _currentUserId);

		if (currentUser is null)
		{
			return NotFound("Current user not found");
		}

		if (!currentUser.IsCurrent)
		{
			currentUser.IsCurrent = true;
		}

		return Ok(currentUser);
	}

	/// <summary>
	/// Updates the current user.
	/// </summary>
	/// <param name="user">The updated user data.</param>
	/// <returns>The updated user, or NotFound if the user does not exist.</returns>
	[HttpPut("update")]
	[Produces("application/json")]
	[ProducesResponseType(typeof(UserData), 200)]
	[ProducesResponseType(404)]
	public ActionResult<UserData> Update([FromBody] UserData user)
	{
		var users = LoadData<List<UserData>>(_usersFilePath);
		var userIndex = users.FindIndex(u => u.Id == _currentUserId);

		if (userIndex != -1)
		{
			users[userIndex] = new UserData
			{
                Id = user.Id,
                UrlProfileImage = user.UrlProfileImage,
                FirstName = user.FirstName,
                SecondName = user.SecondName,
                Tribe = user.Tribe,
                FullName = user.FullName,
                Email = user.Email,
                PhoneNumber = user.PhoneNumber,
                Password = user.Password,
                Age = user.Age,
                Gender = user.Gender,
                CurrentEmployerLocation = user.CurrentEmployerLocation,
                CurrentEmployerCity = user.CurrentEmployerCity,
                CurrentEmployerState = user.CurrentEmployerState,
                CurrentSalaryGrade = user.CurrentSalaryGrade,
                EducationalQualification = user.EducationalQualification,
                SubscriptionTier = user.SubscriptionTier,
                TokenBalance = user.TokenBalance,
                ProfileImageUrl = user.ProfileImageUrl,
                Description = user.Description,
                JoinDate = user.JoinDate,
                IsActive = user.IsActive,
                ReferralCode = user.ReferralCode,
                InvitedFriends = user.InvitedFriends,
                CompletedTransfers = user.CompletedTransfers,
                IsCurrent = user.IsCurrent,
                CurrentEmployer = user.CurrentEmployer,
                CurrentPosition = user.CurrentPosition,
                YearsOfExperience = user.YearsOfExperience,
                Skills = user.Skills,
                Languages = user.Languages,
                Certifications = user.Certifications,
                PreferredWorkType = user.PreferredWorkType,
                IsAvailableForTransfer = user.IsAvailableForTransfer,
                LinkedInProfile = user.LinkedInProfile,
                Portfolio = user.Portfolio,
                ResumeUrl = user.ResumeUrl,
                Bio = user.Bio,
                LastLoginAt = user.LastLoginAt,
                UpdatedAt = user.UpdatedAt

            };

			return Ok(users[userIndex]);
		}

		return NotFound("User not found");
	}

	/// <summary>
	/// Retrieves a user by ID.
	/// </summary>
	/// <param name="id">The user ID.</param>
	/// <returns>The user, or NotFound if the user does not exist.</returns>
	[HttpGet("{id:guid}")]
	[Produces("application/json")]
	[ProducesResponseType(typeof(UserData), 200)]
	[ProducesResponseType(404)]
	public ActionResult<UserData> GetById(Guid id)
	{
		var users = LoadData<List<UserData>>(_usersFilePath);
		var user = users.FirstOrDefault(u => u.Id == id);

		if (user is null)
		{
			return NotFound("User not found");
		}

		return Ok(user);
	}
}

/// <summary>
/// Represents a login request containing email and password.
/// </summary>
public class LoginRequest
{
    /// <summary>
    /// Gets or sets the email address of the user.
    /// </summary>
    public string Email { get; set; }

    /// <summary>
    /// Gets or sets the password of the user.
    /// </summary>
    public string Password { get; set; }
}
