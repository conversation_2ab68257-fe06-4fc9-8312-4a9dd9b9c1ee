using JT.Server.Entities;
using Microsoft.AspNetCore.Mvc;

namespace JT.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TokenController : ChefsControllerBase
{
	private readonly ILogger<TokenController> _logger;

	public TokenController(ILogger<TokenController> logger)
	{
		_logger = logger;
	}

	[HttpGet("user/{userId}/transactions")]
	public async Task<ActionResult<IEnumerable<TokenTransactionData>>> GetUserTokenTransactions(Guid userId)
	{
		try
		{
			var transactions = await GetMockData<TokenTransactionData>("TokenTransactions.json");
			var userTransactions = transactions.Where(t => t.UserId == userId).ToList();
			return Ok(userTransactions);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting token transactions for user {UserId}", userId);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpGet("user/{userId}/balance")]
	public async Task<ActionResult<int>> GetUserTokenBalance(Guid userId)
	{
		try
		{
			var users = await GetMockData<UserData>("Users.json");
			var user = users.FirstOrDefault(u => u.Id == userId);
			
			if (user == null)
			{
				return NotFound();
			}

			return Ok(user.TokenBalance ?? 0);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error getting token balance for user {UserId}", userId);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost("user/{userId}/add")]
	public async Task<ActionResult<TokenTransactionData>> AddTokens(Guid userId, [FromBody] AddTokensRequest request)
	{
		try
		{
			var transaction = new TokenTransactionData
			{
				Id = Guid.NewGuid().ToString(),
				UserId = userId,
				Amount = request.Amount,
				Type = request.Type,
				Description = request.Description,
				ReferenceId = request.ReferenceId,
				CreatedAt = DateTime.UtcNow
			};

			// Update user balance
			var users = await GetMockData<UserData>("Users.json");
			var userIndex = users.FindIndex(u => u.Id == userId);
			if (userIndex != -1)
			{
				users[userIndex].TokenBalance = (users[userIndex].TokenBalance ?? 0) + request.Amount;
				transaction.BalanceAfter = users[userIndex].TokenBalance ?? 0;
				await SaveMockData("Users.json", users);
			}

			// Add transaction
			var transactions = await GetMockData<TokenTransactionData>("TokenTransactions.json");
			var updatedTransactions = transactions.Append(transaction).ToList();
			await SaveMockData("TokenTransactions.json", updatedTransactions);

			return Ok(transaction);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error adding tokens for user {UserId}", userId);
			return StatusCode(500, "Internal server error");
		}
	}

	[HttpPost("user/{userId}/deduct")]
	public async Task<ActionResult<TokenTransactionData>> DeductTokens(Guid userId, [FromBody] DeductTokensRequest request)
	{
		try
		{
			// Check balance first
			var users = await GetMockData<UserData>("Users.json");
			var userIndex = users.FindIndex(u => u.Id == userId);
			if (userIndex == -1)
			{
				return NotFound();
			}

			var currentBalance = users[userIndex].TokenBalance ?? 0;
			if (currentBalance < request.Amount)
			{
				return BadRequest("Insufficient token balance");
			}

			var transaction = new TokenTransactionData
			{
				Id = Guid.NewGuid().ToString(),
				UserId = userId,
				Amount = -request.Amount,
				Type = request.Type,
				Description = request.Description,
				ReferenceId = request.ReferenceId,
				CreatedAt = DateTime.UtcNow
			};

			// Update user balance
			users[userIndex].TokenBalance = currentBalance - request.Amount;
			transaction.BalanceAfter = users[userIndex].TokenBalance ?? 0;
			await SaveMockData("Users.json", users);

			// Add transaction
			var transactions = await GetMockData<TokenTransactionData>("TokenTransactions.json");
			var updatedTransactions = transactions.Append(transaction).ToList();
			await SaveMockData("TokenTransactions.json", updatedTransactions);

			return Ok(transaction);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Error deducting tokens for user {UserId}", userId);
			return StatusCode(500, "Internal server error");
		}
	}
}

public class AddTokensRequest
{
	public int Amount { get; set; }
	public string Type { get; set; } = string.Empty;
	public string Description { get; set; } = string.Empty;
	public string? ReferenceId { get; set; }
}

public class DeductTokensRequest
{
	public int Amount { get; set; }
	public string Type { get; set; } = string.Empty;
	public string Description { get; set; } = string.Empty;
	public string? ReferenceId { get; set; }
}
