using JT.Business.Services.Subscriptions;
using JT.Business.Services.Payments;
using JT.Business.Services.Users;

namespace JT.Presentation;

public partial record SubscriptionModel
{
	private readonly INavigator _navigator;
	private readonly ISubscriptionService _subscriptionService;
	private readonly IPaymentService _paymentService;
	private readonly IUserService _userService;
	private readonly IMessenger _messenger;

	public SubscriptionModel(
		INavigator navigator,
		ISubscriptionService subscriptionService,
		IPaymentService paymentService,
		IUserService userService,
		IMessenger messenger)
	{
		_navigator = navigator;
		_subscriptionService = subscriptionService;
		_paymentService = paymentService;
		_userService = userService;
		_messenger = messenger;
	}

	public IListFeed<SubscriptionPlan> AvailablePlans => ListFeed.Async(_subscriptionService.GetAllPlans);

	public IState<SubscriptionPlan?> CurrentPlan => State.Async(this, GetCurrentPlan);

	public IFeed<User> CurrentUser => _userService.User;

	[ObservableProperty]
	private SubscriptionPlan? _selectedPlan;

	[ObservableProperty]
	private string _selectedPaymentMethod = "thawani";

	[ObservableProperty]
	private bool _isProcessingPayment;

	private async ValueTask<SubscriptionPlan?> GetCurrentPlan(CancellationToken ct)
	{
		var user = await _userService.GetCurrent(ct);
		if (user?.SubscriptionTier != null)
		{
			return await _subscriptionService.GetPlanById(user.SubscriptionTier.ToLower(), ct);
		}
		return null;
	}

	public async ValueTask SelectPlan(SubscriptionPlan plan, CancellationToken ct)
	{
		SelectedPlan = plan;
	}

	public async ValueTask UpgradeToPlan(SubscriptionPlan plan, CancellationToken ct)
	{
		if (IsProcessingPayment) return;

		try
		{
			IsProcessingPayment = true;
			
			var user = await _userService.GetCurrent(ct);
			if (user == null) return;

			// Create payment transaction
			var payment = await _paymentService.CreatePayment(
				user.Id, 
				plan.Id!, 
				SelectedPaymentMethod, 
				ct);

			// Navigate to payment gateway
			await _navigator.NavigateRouteAsync(this, 
				route: "/Main/-/Payment", 
				data: new { PaymentId = payment.Id, PaymentMethod = SelectedPaymentMethod }, 
				cancellation: ct);
		}
		catch (Exception ex)
		{
			// Handle error - show user-friendly message
			await _messenger.Send(new ErrorMessage($"Failed to process payment: {ex.Message}"));
		}
		finally
		{
			IsProcessingPayment = false;
		}
	}

	public async ValueTask ViewPaymentHistory(CancellationToken ct)
	{
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/PaymentHistory", cancellation: ct);
	}

	public async ValueTask ViewFeatureComparison(CancellationToken ct)
	{
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/Features", cancellation: ct);
	}

	public bool CanUpgrade(SubscriptionPlan targetPlan)
	{
		var currentPlan = CurrentPlan.Value;
		if (currentPlan == null) return true;

		// Define upgrade hierarchy
		var planHierarchy = new Dictionary<string, int>
		{
			{ "bronze", 1 },
			{ "silver", 2 },
			{ "gold", 3 },
			{ "diamond", 4 }
		};

		var currentLevel = planHierarchy.GetValueOrDefault(currentPlan.Name?.ToLower() ?? "", 0);
		var targetLevel = planHierarchy.GetValueOrDefault(targetPlan.Name?.ToLower() ?? "", 0);

		return targetLevel > currentLevel;
	}

	public string GetUpgradeButtonText(SubscriptionPlan plan)
	{
		var currentPlan = CurrentPlan.Value;
		if (currentPlan == null) return "Subscribe";
		
		if (currentPlan.Id == plan.Id) return "Current Plan";
		
		return CanUpgrade(plan) ? "Upgrade" : "Downgrade";
	}
}

// Message types for communication
public record ErrorMessage(string Message);
public record SuccessMessage(string Message);
