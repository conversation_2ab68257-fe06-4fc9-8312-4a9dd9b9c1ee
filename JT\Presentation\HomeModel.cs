//using JT.Business.Services.TransferRequests;
using JT.Business.Services.Users;
//using JT.Business.Services.Subscriptions;
//using JT.Business.Services.Tokens;

namespace JT.Presentation;

public partial record HomeModel
{
	private readonly INavigator _navigator;
	//private readonly ITransferRequestService _transferRequestService;
	private readonly IUserService _userService;
	//private readonly ISubscriptionService _subscriptionService;
	//private readonly ITokenService _tokenService;
	private readonly IMessenger _messenger;

	public HomeModel(
		INavigator navigator,
		//ITransferRequestService transferRequestService,
		IUserService userService,
		//ISubscriptionService subscriptionService,
		//ITokenService tokenService,
		IMessenger messenger)
	{
		_navigator = navigator;
		//_transferRequestService = transferRequestService;
		_userService = userService;
		//_subscriptionService = subscriptionService;
		//_tokenService = tokenService;
		_messenger = messenger;
	}

	//public IListState<TransferRequest> TrendingTransfers => ListState
	//	.Async(this, _transferRequestService.GetTrending)
	//	.Observe(_messenger, r => r.Id);

	//public IListFeed<JobCategory> JobCategories => ListFeed.Async(GetJobCategories);

	//public IListFeed<TransferRequest> RecentTransfers => ListFeed.Async(_transferRequestService.GetAll);

	public IListFeed<User> ActiveUsers => ListFeed.Async(_userService.GetPopularCreators);

	public IFeed<User> UserProfile => _userService.User;

	//public IState<SubscriptionPlan?> CurrentSubscription => State.Async(this, GetCurrentSubscription);

	public IState<int> TokenBalance => State.Async(this, GetTokenBalance);

	// Helper methods for data loading
	//private async ValueTask<IImmutableList<JobCategory>> GetJobCategories(CancellationToken ct)
	//{
	//	// This would be implemented when we have a JobCategoryService
	//	// For now, return empty list
	//	return ImmutableList<JobCategory>.Empty;
	//}

	//private async ValueTask<SubscriptionPlan?> GetCurrentSubscription(CancellationToken ct)
	//{
	//	var user = await _userService.GetCurrent(ct);
	//	if (user?.SubscriptionTier != null)
	//	{
	//		return await _subscriptionService.GetPlanById(user.SubscriptionTier.ToLower(), ct);
	//	}
	//	return null;
	//}

	private async ValueTask<int> GetTokenBalance(CancellationToken ct)
	{
		var user = await _userService.GetCurrent(ct);
		return user?.TokenBalance ?? 0;
	}

	// Navigation methods
	public async ValueTask ShowAllTransfers(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/Search", cancellation: ct);

	public async ValueTask ShowRecentTransfers(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/Recent", cancellation: ct);

	//public async ValueTask CategorySearch(JobCategory jobCategory, CancellationToken ct) =>
	//	await _navigator.NavigateRouteAsync(this, route: "/Main/-/Search", data: new { Category = jobCategory }, cancellation: ct);

	//public async ValueTask ViewTransferRequest(TransferRequest transferRequest, CancellationToken ct)
	//{
	//	await _transferRequestService.IncrementViewCount(transferRequest.Id, ct);
	//	await _navigator.NavigateRouteAsync(this, route: $"/Main/-/TransferRequest/{transferRequest.Id}", cancellation: ct);
	//}

	public async ValueTask CreateTransferRequest(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/CreateTransfer", cancellation: ct);

	public async ValueTask ViewSubscriptions(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/Subscriptions", cancellation: ct);

	public async ValueTask ViewTokens(CancellationToken ct) =>
		await _navigator.NavigateRouteAsync(this, route: "/Main/-/Tokens", cancellation: ct);
}
