[{"id": "token_001", "userId": "550e8400-e29b-41d4-a716-446655440001", "amount": 25, "type": "welcome", "description": "Welcome bonus for joining JobTransfer", "referenceId": "welcome_bonus", "createdAt": "2023-01-15T00:00:00Z", "balanceAfter": 25}, {"id": "token_002", "userId": "550e8400-e29b-41d4-a716-446655440001", "amount": 25, "type": "referral", "description": "Referral bonus for inviting <PERSON><PERSON>", "referenceId": "550e8400-e29b-41d4-a716-446655440002", "createdAt": "2023-03-20T00:00:00Z", "balanceAfter": 50}, {"id": "token_003", "userId": "550e8400-e29b-41d4-a716-446655440001", "amount": 50, "type": "purchase", "description": "Purchase bonus for Gold subscription", "referenceId": "gold", "createdAt": "2023-06-01T00:00:00Z", "balanceAfter": 100}, {"id": "token_004", "userId": "550e8400-e29b-41d4-a716-446655440001", "amount": -20, "type": "redemption", "description": "Profile boost for increased visibility", "referenceId": "profile_boost_001", "createdAt": "2024-01-10T00:00:00Z", "balanceAfter": 80}, {"id": "token_005", "userId": "550e8400-e29b-41d4-a716-446655440001", "amount": 25, "type": "referral", "description": "Referral bonus for inviting <PERSON>", "referenceId": "550e8400-e29b-41d4-a716-446655440003", "createdAt": "2022-11-10T00:00:00Z", "balanceAfter": 105}, {"id": "token_006", "userId": "550e8400-e29b-41d4-a716-446655440001", "amount": 45, "type": "bonus", "description": "Completion bonus for successful transfer", "referenceId": "transfer_completion_001", "createdAt": "2024-01-15T00:00:00Z", "balanceAfter": 150}, {"id": "token_007", "userId": "550e8400-e29b-41d4-a716-446655440002", "amount": 25, "type": "welcome", "description": "Welcome bonus for joining JobTransfer", "referenceId": "welcome_bonus", "createdAt": "2023-03-20T00:00:00Z", "balanceAfter": 25}, {"id": "token_008", "userId": "550e8400-e29b-41d4-a716-446655440002", "amount": 25, "type": "purchase", "description": "Purchase bonus for Silver subscription", "referenceId": "silver", "createdAt": "2023-04-01T00:00:00Z", "balanceAfter": 50}, {"id": "token_009", "userId": "550e8400-e29b-41d4-a716-446655440002", "amount": 25, "type": "bonus", "description": "Completion bonus for successful transfer", "referenceId": "transfer_completion_002", "createdAt": "2024-01-12T00:00:00Z", "balanceAfter": 75}, {"id": "token_010", "userId": "550e8400-e29b-41d4-a716-446655440003", "amount": 25, "type": "welcome", "description": "Welcome bonus for joining JobTransfer", "referenceId": "welcome_bonus", "createdAt": "2022-11-10T00:00:00Z", "balanceAfter": 25}, {"id": "token_011", "userId": "550e8400-e29b-41d4-a716-446655440003", "amount": 100, "type": "purchase", "description": "Purchase bonus for Diamond subscription", "referenceId": "diamond", "createdAt": "2022-12-01T00:00:00Z", "balanceAfter": 125}, {"id": "token_012", "userId": "550e8400-e29b-41d4-a716-446655440003", "amount": 75, "type": "referral", "description": "Multiple referral bonuses (3 friends)", "referenceId": "multiple_referrals", "createdAt": "2023-06-15T00:00:00Z", "balanceAfter": 200}, {"id": "token_013", "userId": "550e8400-e29b-41d4-a716-446655440003", "amount": 50, "type": "bonus", "description": "Completion bonus for successful transfers (2x)", "referenceId": "transfer_completion_multiple", "createdAt": "2023-12-20T00:00:00Z", "balanceAfter": 250}]