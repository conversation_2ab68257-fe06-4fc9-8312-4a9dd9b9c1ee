﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
					xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
					xmlns:mobile="http://platform.uno/mobile"
					xmlns:utu="using:Uno.Toolkit.UI"
					xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
					mc:Ignorable="mobile">

	<Style x:Key="ChefsNavigationBarStyle"
		   BasedOn="{StaticResource NavigationBarStyle}"
		   TargetType="utu:NavigationBar">
		<Setter Property="MainCommandStyle" Value="{StaticResource ChefsMainCommandStyle}" />
		<Setter Property="Background" Value="{ThemeResource SurfaceInverseBrush}" />
		<Setter Property="Foreground" Value="{ThemeResource OnSurfaceInverseBrush}" />
		<Setter Property="FontSize" Value="24" />
	</Style>

	<Style x:Key="MaterialAppBarButtonStyle"
		   BasedOn="{StaticResource AppBarButtonStyle}"
		   TargetType="AppBarButton">
		<Setter Property="Foreground" Value="{ThemeResource OnSurfaceInverseBrush}" />
	</Style>

	<Style x:Key="ChefsAppBarButtonStyle"
		   TargetType="AppBarButton"
		   BasedOn="{StaticResource MaterialAppBarButtonStyle}" />

	<Style x:Key="ChefsMainCommandStyle"
		   BasedOn="{StaticResource MaterialMainCommandStyle}"
		   TargetType="AppBarButton">
		<!-- Set the MainCommand content to an empty string to remove the default "Back" text on iOS -->
		<mobile:Setter Property="Content"
					   Value="" />
	</Style>

	<Style x:Key="ChefsModalNavigationBarStyle"
		   BasedOn="{StaticResource ChefsNavigationBarStyle}"
		   TargetType="utu:NavigationBar">
		<Setter Property="utu:ResourceExtensions.Resources">
			<Setter.Value>
				<ResourceDictionary>
					<ResourceDictionary.ThemeDictionaries>
						<ResourceDictionary x:Key="Light">
							<StaticResource x:Key="NavigationBarBackIconData" ResourceKey="Icon_Close" />
						</ResourceDictionary>
						<ResourceDictionary x:Key="Dark">
							<StaticResource x:Key="NavigationBarBackIconData" ResourceKey="Icon_Close" />
						</ResourceDictionary>
					</ResourceDictionary.ThemeDictionaries>
				</ResourceDictionary>
			</Setter.Value>
		</Setter>
	</Style>

	<Style TargetType="utu:NavigationBar"
		   BasedOn="{StaticResource ChefsNavigationBarStyle}" />

	<Style TargetType="AppBarButton"
		   BasedOn="{StaticResource ChefsAppBarButtonStyle}" />
</ResourceDictionary>
