namespace JT.Server.Apis;

/// <summary>
/// Controller for managing token-related operations such as transactions, balances, and token updates.
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class TokenController : JTControllerBase
{
    private readonly string _tokenTransactionsPlansFilePath = "TokenTransactions.json";
    private readonly string _usersFilePath = "Users.json";
    private readonly string _transferRequestsFilePath = "TransferRequests.json";

    /// <summary>
    /// Logger for the TokenController.
    /// </summary>
    private readonly ILogger<TokenController> _logger;

    /// <summary>
    /// Initializes a new instance of the <see cref="TokenController"/> class.
    /// </summary>
    /// <param name="logger">The logger instance for logging operations.</param>
    public TokenController(ILogger<TokenController> logger) => _logger = logger;

    /// <summary>
    /// Retrieves the token transactions for a specific user.
    /// </summary>
    /// <param name="userId">The unique identifier of the user.</param>
    /// <returns>A list of token transactions for the user.</returns>
    [HttpGet("user/{userId:guid}/transactions")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(TokenTransactionData), 200)]
    [ProducesResponseType(404)]
    public ActionResult<IEnumerable<TokenTransactionData>> GetUserTokenTransactions(Guid userId)
    {
        try
        {
            var transactions = LoadData<List<TokenTransactionData>>(_tokenTransactionsPlansFilePath);
            var userTransactions = transactions.Where(t => t.UserId == userId).ToList();
            return Ok(userTransactions.ToImmutableList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting token transactions for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Retrieves the token balance for a specific user.
    /// </summary>
    /// <param name="userId">The unique identifier of the user.</param>
    /// <returns>The token balance of the user.</returns>
    [HttpGet("user/{userId:guid}/balance")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(UserData), 200)]
    [ProducesResponseType(404)]
    public ActionResult<int> GetUserTokenBalance(Guid userId)
    {
        try
        {
            var users = LoadData<List<UserData>>(_usersFilePath);
            var user = users.FirstOrDefault(u => u.Id == userId);

            if (user == null)
            {
                return NotFound();
            }

            return Ok(user.TokenBalance ?? 0);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting token balance for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Adds tokens to a user's account.
    /// </summary>
    /// <param name="userId">The unique identifier of the user.</param>
    /// <param name="request">The request containing token addition details.</param>
    /// <returns>The details of the token transaction.</returns>
    [HttpPost("user/{userId:guid}/add")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(UserData), 200)]
    [ProducesResponseType(typeof(TokenTransactionData), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<TokenTransactionData>> AddTokens(Guid userId, [FromBody] AddTokensRequest request)
    {
        try
        {
            var transaction = new TokenTransactionData
            {
                Id = Guid.NewGuid().ToString(),
                UserId = userId,
                Amount = request.Amount,
                Type = request.Type,
                Description = request.Description,
                ReferenceId = request.ReferenceId,
                CreatedAt = DateTime.UtcNow
            };

            // Update user balance
            var users = LoadData<List<UserData>>(_usersFilePath);
            var userIndex = users.FindIndex(u => u.Id == userId);
            if (userIndex != -1)
            {
                users[userIndex].TokenBalance = (users[userIndex].TokenBalance ?? 0) + request.Amount;
                transaction.BalanceAfter = users[userIndex].TokenBalance ?? 0;
                await SaveMockData(_usersFilePath, users);
            }

            // Add transaction
            var transactions = LoadData<List<TokenTransactionData>>(_tokenTransactionsPlansFilePath);
            var updatedTransactions = transactions.Append(transaction).ToList();
            await SaveMockData(_tokenTransactionsPlansFilePath, updatedTransactions);

            return Ok(transaction);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding tokens for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Deducts tokens from a user's account.
    /// </summary>
    /// <param name="userId">The unique identifier of the user.</param>
    /// <param name="request">The request containing token deduction details.</param>
    /// <returns>The details of the token transaction.</returns>
    [HttpPost("user/{userId:guid}/deduct")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(UserData), 200)]
    [ProducesResponseType(typeof(TokenTransactionData), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<TokenTransactionData>> DeductTokens(Guid userId, [FromBody] DeductTokensRequest request)
    {
        try
        {
            // Check balance first
            var users = LoadData<List<UserData>>(_usersFilePath);
            var userIndex = users.FindIndex(u => u.Id == userId);
            if (userIndex == -1)
            {
                return NotFound();
            }

            var currentBalance = users[userIndex].TokenBalance ?? 0;
            if (currentBalance < request.Amount)
            {
                return BadRequest("Insufficient token balance");
            }

            var transaction = new TokenTransactionData
            {
                Id = Guid.NewGuid().ToString(),
                UserId = userId,
                Amount = -request.Amount,
                Type = request.Type,
                Description = request.Description,
                ReferenceId = request.ReferenceId,
                CreatedAt = DateTime.UtcNow
            };

            // Update user balance
            users[userIndex].TokenBalance = currentBalance - request.Amount;
            transaction.BalanceAfter = users[userIndex].TokenBalance ?? 0;
            await SaveMockData(_usersFilePath, users);

            // Add transaction
            var transactions = LoadData<List<TokenTransactionData>>(_tokenTransactionsPlansFilePath);
            var updatedTransactions = transactions.Append(transaction).ToList();
            await SaveMockData(_tokenTransactionsPlansFilePath, updatedTransactions);

            return Ok(transaction);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deducting tokens for user {UserId}", userId);
            return StatusCode(500, "Internal server error");
        }
    }
}

/// <summary>
/// Request model for adding tokens to a user's account.
/// </summary>
public class AddTokensRequest
{
    /// <summary>
    /// The amount of tokens to add.
    /// </summary>
    public int Amount { get; set; }

    /// <summary>
    /// The type of the token transaction.
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// A description of the token transaction.
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// An optional reference ID for the token transaction.
    /// </summary>
    public string? ReferenceId { get; set; }
}

/// <summary>
/// Request model for deducting tokens from a user's account.
/// </summary>
public class DeductTokensRequest
{
    /// <summary>
    /// The amount of tokens to deduct.
    /// </summary>
    public int Amount { get; set; }

    /// <summary>
    /// The type of the token transaction.
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// A description of the token transaction.
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// An optional reference ID for the token transaction.
    /// </summary>
    public string? ReferenceId { get; set; }
}
