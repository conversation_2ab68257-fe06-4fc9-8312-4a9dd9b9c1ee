namespace JT.Server.Entities;

public class TransferRequestData
{
	public Guid Id { get; set; }
	public Guid UserId { get; set; }
	public string? RequestTitle { get; set; }
	public LocationData? CurrentLocation { get; set; }
	public LocationData? DestinationLocation { get; set; }
	public string? CurrentSalaryGrade { get; set; }
	public string? DesiredSalaryGrade { get; set; }
	public string? CurrentFinancialGrade { get; set; }
	public string? DesiredFinancialGrade { get; set; }
	public string? Industry { get; set; }
	public CategoryData? JobCategory { get; set; }
	public string? TransferReason { get; set; }
	public string? Status { get; set; }
	public string? Priority { get; set; }
	public DateTime SubmissionDate { get; set; }
	public DateTime ExpirationDate { get; set; }
	public int ViewCount { get; set; }
	public int InterestedEmployers { get; set; }
	public List<string>? Documents { get; set; }
	public List<string>? RequiredSkills { get; set; }
	public string? PreferredCompanySize { get; set; }
	public string? RemoteWorkPreference { get; set; }
	public List<string>? LanguageRequirements { get; set; }
	public UserProfileData? CreatedBy { get; set; }
	public List<EmployerResponseData>? Responses { get; set; }
}

public class LocationData
{
	public string? City { get; set; }
	public string? State { get; set; }
	public string? Country { get; set; }
}

public class UserProfileData
{
	public Guid Id { get; set; }
	public string? FullName { get; set; }
	public string? ProfileImageUrl { get; set; }
	public string? CurrentPosition { get; set; }
	public string? Experience { get; set; }
}

public class EmployerResponseData
{
	public string? Id { get; set; }
	public string? EmployerId { get; set; }
	public string? CompanyName { get; set; }
	public DateTime ResponseDate { get; set; }
	public string? Status { get; set; }
	public string? Message { get; set; }
	public string? OfferedSalary { get; set; }
	public bool InterviewScheduled { get; set; }
}
