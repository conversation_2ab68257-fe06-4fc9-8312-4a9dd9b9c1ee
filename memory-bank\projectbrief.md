# Project Brief - JT Recipe & Cookbook Application

## Project Overview
JT is a cross-platform recipe and cookbook management application built using the Uno Platform with .NET 9. The application enables users to discover, save, organize, and share recipes across multiple platforms including Windows, Android, iOS, WebAssembly, and Desktop.

## Core Requirements

### Functional Requirements
1. **Recipe Management**
   - Browse and search recipes
   - View detailed recipe information (ingredients, steps, nutrition, reviews)
   - Save favorite recipes
   - Rate and review recipes

2. **Cookbook Management**
   - Create custom cookbooks
   - Organize recipes into collections
   - Share cookbooks with other users

3. **User Management**
   - User authentication and registration
   - User profiles and preferences
   - Social features (following, sharing)

4. **Cross-Platform Support**
   - Native experience on Windows, Android, iOS
   - Web application via WebAssembly
   - Desktop application support

### Technical Requirements
1. **Architecture**: MVVM pattern with clean separation of concerns
2. **Backend**: ASP.NET Core Web API with RESTful endpoints
3. **Authentication**: IdentityServer4 integration (needs migration to Duende)
4. **Data**: JSON-based development data, designed for future database integration
5. **UI Framework**: Uno Platform with XAML-based UI
6. **Testing**: Comprehensive unit and UI testing coverage

## Project Goals

### Primary Goals
- Create a modern, intuitive recipe management experience
- Provide seamless cross-platform functionality
- Enable social recipe sharing and discovery
- Maintain high code quality and testability

### Success Criteria
- Application runs on all target platforms
- User authentication and data persistence work reliably
- Recipe browsing and management features are fully functional
- Code coverage exceeds 80%
- Documentation is comprehensive and up-to-date

## Current Status
**Phase**: Early Development / Foundation Building
**Priority**: Completing core infrastructure and implementing comprehensive testing

## Key Stakeholders
- **Development Team**: Primary implementers
- **End Users**: Recipe enthusiasts and home cooks
- **Platform**: Uno Platform ecosystem

## Project Constraints
- Must support multiple platforms through single codebase
- Authentication system needs modernization
- Limited initial budget for external services
- Development team learning Uno Platform patterns

## Success Metrics
- Cross-platform deployment success rate
- User engagement with recipe features
- Application performance across platforms
- Code quality metrics (coverage, maintainability)
- Time to implement new features

## Risk Factors
- **Technical Debt**: Significant commented-out code and incomplete implementations
- **Testing Gap**: Minimal test coverage currently
- **Documentation**: Severely lacking project documentation
- **Dependencies**: Some pre-release packages in use
- **Security**: Deprecated IdentityServer4 needs migration
