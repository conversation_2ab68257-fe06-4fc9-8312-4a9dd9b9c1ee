﻿<Page x:Class="JT.Presentation.WelcomePage"
	  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:android="http://uno.ui/android#using:Uno.Toolkit.UI"
	  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
	  xmlns:ios="http://uno.ui/ios#using:Uno.Toolkit.UI"
	  xmlns:local="using:JT.Presentation"
	  xmlns:ctrl="using:JT.Presentation.Controls"
	  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
	  xmlns:muxc="using:Microsoft.UI.Xaml.Controls"
	  xmlns:uen="using:Uno.Extensions.Navigation.UI"
	  xmlns:uer="using:Uno.Extensions.Reactive.UI"
	  xmlns:ut="using:Uno.Themes"
	  xmlns:utu="using:Uno.Toolkit.UI"
	  android:StatusBar.Background="{ThemeResource SurfaceBrush}"
	  android:StatusBar.Foreground="Auto"
	  ios:StatusBar.Foreground="Auto"
	  NavigationCacheMode="Required"
	  mc:Ignorable="d android ios"
	  Background="{ThemeResource SurfaceBrush}">

	<utu:AutoLayout utu:SafeArea.Insets="VisibleBounds"
					Orientation="{utu:Responsive Normal=Vertical,
												 Wide=Horizontal}">
		<FlipView IsEnabled="False"
				  Visibility="{utu:Responsive Normal=Collapsed,
											  Wide=Visible}"
				  utu:AutoLayout.PrimaryAlignment="Stretch"
				  SelectedIndex="{Binding Pages.CurrentIndex}">
			<FlipView.Items>
				<!-- First Splash image -->
				<Image Source="ms-appx:///Assets/Welcome/Wide/first_splash_screen.jpg"
					   Stretch="UniformToFill" />

				<!-- Second Splash image -->
				<Image Source="ms-appx:///Assets/Welcome/Wide/second_splash_screen.jpg"
					   Stretch="UniformToFill" />

				<!-- Third Splash image -->
				<Image Source="ms-appx:///Assets/Welcome/Wide/third_splash_screen.jpg"
					   Stretch="UniformToFill" />
			</FlipView.Items>
		</FlipView>

		<utu:AutoLayout utu:AutoLayout.PrimaryAlignment="Stretch">
			<FlipView x:Name="flipView"
					  utu:AutoLayout.PrimaryAlignment="Stretch"
					  Background="Transparent"
					  utu:SelectorExtensions.PipsPager="{Binding ElementName=pipsPager}"
					  SelectedIndex="{Binding Pages.CurrentIndex, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
				<FlipView.Items>
					<ctrl:WelcomeView ImageUrl="ms-appx:///Assets/Welcome/first_splash_screen.png"
									  Title="Welcome to your App!"
									  VerticalContentAlignment="Bottom"
									  Description="Embark on a delightful coding journey as you discover, create, and share awesome script tailored to your app and project preferences." />
					<ctrl:WelcomeView ImageUrl="ms-appx:///Assets/Welcome/second_splash_screen.png"
									  VerticalContentAlignment="Bottom"
									  Title="Explore Thousands of Recipes"
									  Description="Find your next culinary adventure or last minute lunch from our vast collection of diverse and mouth-watering recipes." />
					<ctrl:WelcomeView ImageUrl="ms-appx:///Assets/Welcome/third_splash_screen.png"
									  Title="Personalize Your Recipe Journey"
									  VerticalContentAlignment="Bottom"
									  Description="Create your own recipe collections, cookbooks, follow other foodies, and share your creations with the Chefs community." />
				</FlipView.Items>
			</FlipView>
			<utu:AutoLayout Padding="32,0,32,15"
							PrimaryAxisAlignment="End"
							Spacing="16">
				<!-- Pips -->
				<muxc:PipsPager x:Name="pipsPager"
								Margin="0,0,0,10"
								utu:AutoLayout.CounterAlignment="Center"
								NumberOfPages="3"
								MaxVisiblePips="3"
								Orientation="Horizontal"
								Style="{StaticResource PipsPagerStyle}" />

				<!-- Buttons -->
				<utu:AutoLayout Spacing="16"
								PrimaryAxisAlignment="Center">
					<utu:AutoLayout Spacing="16"
									PrimaryAxisAlignment="Center"
									CounterAxisAlignment="Center"
									Orientation="Horizontal">
						<Button HorizontalContentAlignment="Center"
								VerticalContentAlignment="Center"
								Content="Previous"
								utu:AutoLayout.PrimaryAlignment="Stretch"
								utu:FlipViewExtensions.Previous="{Binding ElementName=flipView}"
								Style="{StaticResource ChefsOutlinedButtonStyle}"
								IsEnabled="{Binding Pages.Value.CanMovePrevious}" />
						<Button HorizontalContentAlignment="Center"
								VerticalContentAlignment="Center"
								Content="Next"
								utu:FlipViewExtensions.Next="{Binding ElementName=flipView}"
								utu:AutoLayout.PrimaryAlignment="Stretch"
								Style="{StaticResource ChefsPrimaryButtonStyle}"
								IsEnabled="{Binding Pages.Value.CanMoveNext}" />
					</utu:AutoLayout>
					<Button HorizontalContentAlignment="Center"
							VerticalContentAlignment="Center"
							Content="Skip"
							Padding="12,20"
							uen:Navigation.Request="-/Login"
							Foreground="{ThemeResource PrimaryBrush}"
							CornerRadius="4"
							AutomationProperties.AutomationId="SkipButton"
							x:Name="SkipButton"
							Style="{StaticResource TextButtonStyle}" />
				</utu:AutoLayout>
			</utu:AutoLayout>
		</utu:AutoLayout>
	</utu:AutoLayout>
</Page>
