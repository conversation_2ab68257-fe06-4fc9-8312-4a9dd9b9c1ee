﻿<Page x:Class="JT.Presentation.MainPage"
	  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	  xmlns:local="using:JT.Presentation"
	  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
	  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
	  xmlns:win="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  mc:Ignorable="d"
	  xmlns:uen="using:Uno.Extensions.Navigation.UI"
	  xmlns:utu="using:Uno.Toolkit.UI"
	  xmlns:ut="using:Uno.Themes"
	  xmlns:not_skia="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	  Background="{ThemeResource BackgroundBrush}">
    <Page.Resources>
        <x:Double x:Key="NavigationTabBarItemIconHeight">24</x:Double>
    </Page.Resources>
    <Grid uen:Region.Attached="True"
		  utu:AutoLayout.PrimaryAlignment="Stretch">

        <Grid.RowDefinitions>
            <RowDefinition />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <Grid Grid.Row="0"
			  Grid.Column="1"
			  uen:Region.Attached="True"
			  x:Name="RootGrid"
			  uen:Region.Navigator="Visibility" />

        <utu:TabBar Grid.Row="1"
					Grid.Column="1"
					Visibility="{utu:Responsive Normal=Visible,
												Wide=Collapsed}"
					uen:Region.Attached="True"
					Style="{StaticResource BottomTabBarStyle}">
            <utu:TabBarItem uen:Region.Name="Home"
							utu:TabBarItemExtensions.OnClickBehaviors="BackNavigation"
							Content="Home">
                <utu:TabBarItem.Icon>
                    <PathIcon Data="{StaticResource Icon_Home}" />
                </utu:TabBarItem.Icon>
            </utu:TabBarItem>

            <utu:TabBarItem uen:Region.Name="-/Search"
							utu:TabBarItemExtensions.OnClickBehaviors="BackNavigation"
							Content="Search">
                <utu:TabBarItem.Icon>
                    <PathIcon Data="{StaticResource Icon_Search}" />
                </utu:TabBarItem.Icon>
            </utu:TabBarItem>

            <utu:TabBarItem uen:Region.Name="FavoriteRecipes"
							utu:TabBarItemExtensions.OnClickBehaviors="BackNavigation"
							Content="Favorites">
                <utu:TabBarItem.Icon>
                    <PathIcon Data="{StaticResource Icon_Heart}" />
                </utu:TabBarItem.Icon>
            </utu:TabBarItem>
        </utu:TabBar>

        <utu:AutoLayout Grid.RowSpan="2"
						Background="{ThemeResource SurfaceBrush}"
						Visibility="{utu:Responsive Normal=Collapsed,
													Wide=Visible}"
						Width="120">
            <utu:TabBar uen:Region.Attached="True"
						Style="{StaticResource VerticalTabBarStyle}"
						utu:AutoLayout.PrimaryAlignment="Stretch">

                <utu:TabBarItem uen:Region.Name="Home"
								utu:TabBarItemExtensions.OnClickBehaviors="BackNavigation"
								utu:TabBarItemExtensions.OnClickBehaviorsTarget="{Binding ElementName=RootGrid}"
								Content="Home">
                    <utu:TabBarItem.Icon>
                        <PathIcon Data="{StaticResource Icon_Home}" />
                    </utu:TabBarItem.Icon>
                </utu:TabBarItem>

                <utu:TabBarItem uen:Region.Name="-/Search"
								utu:TabBarItemExtensions.OnClickBehaviors="BackNavigation"
								utu:TabBarItemExtensions.OnClickBehaviorsTarget="{Binding ElementName=RootGrid}"
								Content="Search">
                    <utu:TabBarItem.Icon>
                        <PathIcon Data="{StaticResource Icon_Search}" />
                    </utu:TabBarItem.Icon>
                </utu:TabBarItem>

                <utu:TabBarItem uen:Region.Name="FavoriteRecipes"
								utu:TabBarItemExtensions.OnClickBehaviors="BackNavigation"
								utu:TabBarItemExtensions.OnClickBehaviorsTarget="{Binding ElementName=RootGrid}"
								Content="Favorites">
                    <utu:TabBarItem.Icon>
                        <PathIcon Data="{StaticResource Icon_Heart}" />
                    </utu:TabBarItem.Icon>
                </utu:TabBarItem>
            </utu:TabBar>
        </utu:AutoLayout>
    </Grid>
</Page>
